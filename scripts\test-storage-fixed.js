const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");

// Load environment variables manually
const envPath = path.join(__dirname, "..", ".env");
const envContent = fs.readFileSync(envPath, "utf8");
const envVars = {};

envContent.split("\n").forEach((line) => {
  const [key, value] = line.split("=");
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});

const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey =
  envVars.SUPABASE_SERVICE_ROLE_KEY || envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing Supabase environment variables");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testStorageFixed() {
  console.log("🧪 Testing storage with correct configuration...");
  console.log("Bucket: images");
  console.log("Folder: imageFolder/gallery/");
  
  try {
    // Test 1: Check bucket exists
    console.log("\n1. Checking 'images' bucket...");
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();

    if (bucketsError) {
      console.error('❌ Error accessing storage:', bucketsError.message);
      return;
    }

    const imagesBucket = buckets.find(bucket => bucket.name === 'images');
    if (!imagesBucket) {
      console.error('❌ Images bucket not found!');
      console.log('📝 Run: node scripts/setup-storage.js');
      return;
    }
    
    console.log('✅ Images bucket exists');
    console.log(`   Public: ${imagesBucket.public}`);

    // Test 2: List files in correct folder
    console.log("\n2. Testing folder access...");
    const { data: files, error: listError } = await supabase.storage
      .from('images')
      .list('imageFolder/gallery', { limit: 10 });

    if (listError) {
      console.error('❌ Error accessing imageFolder/gallery:', listError.message);
      if (listError.message.includes('not found')) {
        console.log('📝 Folder structure needs to be created');
      }
    } else {
      console.log('✅ Successfully accessed imageFolder/gallery folder');
      console.log(`   Found ${files.length} files`);
    }

    // Test 3: Test upload to correct path
    console.log("\n3. Testing file upload...");
    const testFileName = `test-${Date.now()}.txt`;
    const testFilePath = `imageFolder/gallery/${testFileName}`;
    const testContent = Buffer.from("Test file for gallery upload");

    const { error: uploadError } = await supabase.storage
      .from('images')
      .upload(testFilePath, testContent, {
        contentType: 'text/plain'
      });

    if (uploadError) {
      console.error('❌ Upload failed:', uploadError.message);
      
      if (uploadError.message.includes('Bucket not found')) {
        console.log('📝 Solution: Run node scripts/setup-storage.js');
      } else if (uploadError.message.includes('not allowed')) {
        console.log('📝 Solution: Check bucket policies in Supabase dashboard');
      }
    } else {
      console.log('✅ Upload successful');
      
      // Test 4: Test public URL generation
      console.log("\n4. Testing public URL...");
      const { data: urlData } = supabase.storage
        .from('images')
        .getPublicUrl(testFilePath);

      if (urlData && urlData.publicUrl) {
        console.log('✅ Public URL generated successfully');
        console.log(`   URL: ${urlData.publicUrl}`);
      } else {
        console.log('❌ Failed to generate public URL');
      }

      // Test 5: Test file deletion
      console.log("\n5. Testing file deletion...");
      const { error: deleteError } = await supabase.storage
        .from('images')
        .remove([testFilePath]);

      if (deleteError) {
        console.error('❌ Delete failed:', deleteError.message);
      } else {
        console.log('✅ File deleted successfully');
      }
    }

    // Test 6: Simulate actual image upload path
    console.log("\n6. Testing actual image upload path...");
    const imageFileName = `${Date.now()}-test.jpg`;
    const imageFilePath = `imageFolder/gallery/${imageFileName}`;
    
    // Create a small fake image buffer
    const fakeImageBuffer = Buffer.from("fake-jpeg-data");
    
    const { error: imageUploadError } = await supabase.storage
      .from('images')
      .upload(imageFilePath, fakeImageBuffer, {
        contentType: 'image/jpeg'
      });

    if (imageUploadError) {
      console.error('❌ Image upload simulation failed:', imageUploadError.message);
    } else {
      console.log('✅ Image upload simulation successful');
      
      // Get the public URL that would be used in the app
      const { data: imageUrlData } = supabase.storage
        .from('images')
        .getPublicUrl(imageFilePath);
      
      console.log(`   Image URL: ${imageUrlData.publicUrl}`);
      
      // Clean up
      await supabase.storage
        .from('images')
        .remove([imageFilePath]);
      
      console.log('✅ Cleanup completed');
    }

    console.log('\n🎉 Storage test completed!');
    
    // Summary
    console.log('\n📋 Summary:');
    console.log('✅ Bucket: images');
    console.log('✅ Folder: imageFolder/gallery/');
    console.log('✅ Upload path working');
    console.log('✅ Public URL generation working');
    console.log('✅ File deletion working');
    
    console.log('\n🚀 Your gallery upload should now work!');
    console.log('Try uploading an image through the admin panel.');

  } catch (error) {
    console.error('❌ Unexpected error:', error);
    console.log('\n📝 Troubleshooting:');
    console.log('1. Check your .env file has correct Supabase credentials');
    console.log('2. Run: node scripts/setup-storage.js');
    console.log('3. Check Supabase dashboard for bucket and policies');
  }
}

// Run the test
if (require.main === module) {
  testStorageFixed();
}

module.exports = { testStorageFixed };
