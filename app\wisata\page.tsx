"use client";

import { useEffect, useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { supabase } from "@/lib/supabase";
import { 
  Search, 
  MapPin, 
  Clock, 
  Phone, 
  Star, 
  Filter,
  Ticket,
  Wifi,
  Car
} from "lucide-react";

interface Tourism {
  id: string;
  name: string;
  description: string;
  category: string;
  location: string;
  facilities: string | null;
  opening_hours: string | null;
  ticket_price: string | null;
  contact_info: string | null;
  image_url: string;
  is_featured: boolean;
  created_at: string;
}

export default function WisataPage() {
  const [tourismList, setTourismList] = useState<Tourism[]>([]);
  const [filteredTourism, setFilteredTourism] = useState<Tourism[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const categories = [
    "Wisata Alam",
    "Wisata Budaya", 
    "Wisata Religi",
    "Wisata Kuliner",
    "Wisata Sejarah",
    "Wisata Edukasi",
    "Lainnya",
  ];

  useEffect(() => {
    fetchTourism();
  }, []);

  useEffect(() => {
    filterTourism();
  }, [tourismList, searchTerm, selectedCategory]);

  const fetchTourism = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("tourism")
        .select("*")
        .order("is_featured", { ascending: false })
        .order("created_at", { ascending: false });

      if (error) throw error;
      setTourismList(data || []);
    } catch (error) {
      console.error("Error fetching tourism:", error);
    } finally {
      setLoading(false);
    }
  };

  const filterTourism = () => {
    let filtered = tourismList;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(
        (tourism) =>
          tourism.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tourism.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tourism.location.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter((tourism) => tourism.category === selectedCategory);
    }

    setFilteredTourism(filtered);
  };

  const formatFacilities = (facilities: string | null) => {
    if (!facilities) return [];
    return facilities.split(",").map(f => f.trim()).filter(f => f.length > 0);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Memuat data wisata...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Wisata Pakan Rabaa Utara Duo
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Jelajahi keindahan alam, budaya, dan kuliner khas nagari kami. 
              Temukan destinasi wisata menarik yang siap memukau Anda.
            </p>
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                placeholder="Cari wisata berdasarkan nama, deskripsi, atau lokasi..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="md:w-64">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue placeholder="Pilih kategori" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Kategori</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Featured Tourism */}
        {filteredTourism.some(t => t.is_featured) && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Star className="w-6 h-6 text-yellow-500 mr-2" />
              Wisata Unggulan
            </h2>
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {filteredTourism
                .filter(tourism => tourism.is_featured)
                .map((tourism) => (
                  <Card key={tourism.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="relative">
                      <img
                        src={tourism.image_url}
                        alt={tourism.name}
                        className="w-full h-64 object-cover"
                      />
                      <div className="absolute top-4 right-4">
                        <Badge className="bg-yellow-500 text-white">
                          <Star className="w-3 h-3 mr-1" />
                          Unggulan
                        </Badge>
                      </div>
                      <div className="absolute top-4 left-4">
                        <Badge variant="secondary">{tourism.category}</Badge>
                      </div>
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">
                        {tourism.name}
                      </h3>
                      <p className="text-gray-600 mb-4 flex items-start">
                        <MapPin className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
                        {tourism.location}
                      </p>
                      <p className="text-gray-700 mb-4 line-clamp-3">
                        {tourism.description}
                      </p>
                      
                      <div className="space-y-2 text-sm">
                        {tourism.opening_hours && (
                          <div className="flex items-center text-gray-600">
                            <Clock className="w-4 h-4 mr-2" />
                            <span>{tourism.opening_hours}</span>
                          </div>
                        )}
                        {tourism.ticket_price && (
                          <div className="flex items-center text-gray-600">
                            <Ticket className="w-4 h-4 mr-2" />
                            <span>{tourism.ticket_price}</span>
                          </div>
                        )}
                        {tourism.contact_info && (
                          <div className="flex items-center text-gray-600">
                            <Phone className="w-4 h-4 mr-2" />
                            <span>{tourism.contact_info}</span>
                          </div>
                        )}
                      </div>

                      {tourism.facilities && (
                        <div className="mt-4">
                          <p className="text-sm font-medium text-gray-900 mb-2">Fasilitas:</p>
                          <div className="flex flex-wrap gap-1">
                            {formatFacilities(tourism.facilities).slice(0, 3).map((facility, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {facility}
                              </Badge>
                            ))}
                            {formatFacilities(tourism.facilities).length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{formatFacilities(tourism.facilities).length - 3} lainnya
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
            </div>
          </div>
        )}

        {/* All Tourism */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            {selectedCategory === "all" ? "Semua Wisata" : selectedCategory}
            <span className="text-lg font-normal text-gray-600 ml-2">
              ({filteredTourism.filter(t => !t.is_featured || selectedCategory !== "all").length} destinasi)
            </span>
          </h2>
          
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredTourism
              .filter(tourism => !tourism.is_featured || selectedCategory !== "all")
              .map((tourism) => (
                <Card key={tourism.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative">
                    <img
                      src={tourism.image_url}
                      alt={tourism.name}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge variant="secondary">{tourism.category}</Badge>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="text-lg font-bold text-gray-900 mb-2">
                      {tourism.name}
                    </h3>
                    <p className="text-gray-600 mb-3 flex items-start text-sm">
                      <MapPin className="w-4 h-4 mr-2 mt-0.5 flex-shrink-0" />
                      {tourism.location}
                    </p>
                    <p className="text-gray-700 mb-3 text-sm line-clamp-2">
                      {tourism.description}
                    </p>
                    
                    <div className="space-y-1 text-xs">
                      {tourism.opening_hours && (
                        <div className="flex items-center text-gray-600">
                          <Clock className="w-3 h-3 mr-2" />
                          <span>{tourism.opening_hours}</span>
                        </div>
                      )}
                      {tourism.ticket_price && (
                        <div className="flex items-center text-gray-600">
                          <Ticket className="w-3 h-3 mr-2" />
                          <span>{tourism.ticket_price}</span>
                        </div>
                      )}
                    </div>

                    {tourism.facilities && (
                      <div className="mt-3">
                        <div className="flex flex-wrap gap-1">
                          {formatFacilities(tourism.facilities).slice(0, 2).map((facility, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {facility}
                            </Badge>
                          ))}
                          {formatFacilities(tourism.facilities).length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{formatFacilities(tourism.facilities).length - 2}
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
          </div>
        </div>

        {/* No Results */}
        {filteredTourism.length === 0 && (
          <div className="text-center py-12">
            <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              Tidak ada wisata ditemukan
            </h3>
            <p className="text-gray-600 mb-4">
              Coba ubah kata kunci pencarian atau filter kategori
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("");
                setSelectedCategory("all");
              }}
            >
              Reset Filter
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
