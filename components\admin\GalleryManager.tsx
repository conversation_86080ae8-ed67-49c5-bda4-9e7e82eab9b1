"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ImageUpload } from "@/components/ui/image-upload";
import { supabase } from "@/lib/supabase";
import {
  Plus,
  Edit2,
  Trash2,
  Image as ImageIcon,
  Calendar,
} from "lucide-react";

interface GalleryItem {
  id: string;
  title: string;
  description: string;
  image_url: string;
  activity_type: string;
  activity_date: string;
  created_at: string;
  updated_at: string;
}

export function GalleryManager() {
  const [galleryItems, setGalleryItems] = useState<GalleryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<GalleryItem | null>(null);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    image_url: "",
    activity_type: "Kegiatan Umum",
    activity_date: new Date().toISOString().split("T")[0], // Today's date in YYYY-MM-DD format
  });

  const activityTypes = [
    "Kegiatan Umum",
    "Gotong Royong",
    "Pelatihan",
    "Budaya",
    "Olahraga",
    "Pendidikan",
    "Kesehatan",
    "Ekonomi",
    "Lingkungan",
    "Lainnya",
  ];

  useEffect(() => {
    fetchGalleryItems();
  }, []);

  const fetchGalleryItems = async () => {
    try {
      const { data } = await supabase
        .from("gallery")
        .select("*")
        .order("created_at", { ascending: false });

      setGalleryItems(data || []);
    } catch (error) {
      console.error("Error fetching gallery items:", error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      image_url: "",
      activity_type: "Kegiatan Umum",
      activity_date: new Date().toISOString().split("T")[0],
    });
    setEditingItem(null);
  };

  const openDialog = (item?: GalleryItem) => {
    if (item) {
      setEditingItem(item);
      setFormData({
        title: item.title,
        description: item.description,
        image_url: item.image_url,
        activity_type: item.activity_type,
        activity_date: item.activity_date,
      });
    } else {
      resetForm();
    }
    setDialogOpen(true);
  };

  const handleSave = async () => {
    if (
      !formData.title.trim() ||
      !formData.description.trim() ||
      !formData.image_url.trim() ||
      !formData.activity_date.trim()
    ) {
      alert("Semua field harus diisi!");
      return;
    }

    setSaving(true);
    try {
      if (editingItem) {
        // Update (excluding image_url as per requirement)
        const { error } = await supabase
          .from("gallery")
          .update({
            title: formData.title,
            description: formData.description,
            activity_type: formData.activity_type,
            activity_date: formData.activity_date,
            updated_at: new Date().toISOString(),
          })
          .eq("id", editingItem.id);

        if (error) throw error;
      } else {
        // Create
        const { error } = await supabase.from("gallery").insert([formData]);

        if (error) throw error;
      }

      await fetchGalleryItems();
      setDialogOpen(false);
      resetForm();
      alert("Data galeri berhasil disimpan!");
    } catch (error) {
      console.error("Error saving gallery item:", error);
      alert("Gagal menyimpan data galeri");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Apakah Anda yakin ingin menghapus item galeri ini?")) {
      return;
    }

    try {
      const { error } = await supabase.from("gallery").delete().eq("id", id);

      if (error) throw error;

      await fetchGalleryItems();
      alert("Item galeri berhasil dihapus!");
    } catch (error) {
      console.error("Error deleting gallery item:", error);
      alert("Gagal menghapus item galeri");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("id-ID", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Memuat galeri...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Kelola Galeri</h2>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => openDialog()}>
              <Plus className="w-4 h-4 mr-2" />
              Tambah Galeri
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-screen overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingItem ? "Edit Galeri" : "Tambah Galeri"}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="title">Judul</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) =>
                    setFormData({ ...formData, title: e.target.value })
                  }
                  placeholder="Judul kegiatan"
                />
              </div>
              <div>
                <Label htmlFor="description">Deskripsi</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  placeholder="Deskripsi kegiatan"
                  rows={3}
                />
              </div>
              <div>
                <Label htmlFor="activity_type">Jenis Kegiatan</Label>
                <Select
                  value={formData.activity_type}
                  onValueChange={(value) =>
                    setFormData({ ...formData, activity_type: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih jenis kegiatan" />
                  </SelectTrigger>
                  <SelectContent>
                    {activityTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="activity_date">Tanggal Kegiatan</Label>
                <Input
                  id="activity_date"
                  type="date"
                  value={formData.activity_date}
                  onChange={(e) =>
                    setFormData({ ...formData, activity_date: e.target.value })
                  }
                />
              </div>

              {/* Image upload - only show for new items, not for editing */}
              {!editingItem && (
                <ImageUpload
                  onImageUploaded={(url) =>
                    setFormData({ ...formData, image_url: url })
                  }
                  currentImageUrl={formData.image_url}
                />
              )}

              {/* Show current image for editing but don't allow changes */}
              {editingItem && (
                <div>
                  <Label>Gambar Saat Ini</Label>
                  <div className="w-full h-48 bg-gray-100 rounded-lg overflow-hidden">
                    <img
                      src={editingItem.image_url}
                      alt={editingItem.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    Gambar tidak dapat diubah. Untuk mengganti gambar, hapus
                    item ini dan buat yang baru.
                  </p>
                </div>
              )}

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setDialogOpen(false)}>
                  Batal
                </Button>
                <Button onClick={handleSave} disabled={saving}>
                  {saving ? "Menyimpan..." : "Simpan"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Gallery Items Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {galleryItems.length > 0 ? (
          galleryItems.map((item) => (
            <Card key={item.id} className="overflow-hidden">
              <div className="h-48 bg-gray-200 overflow-hidden">
                <img
                  src={item.image_url}
                  alt={item.title}
                  className="w-full h-full object-cover"
                />
              </div>
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg line-clamp-2">
                    {item.title}
                  </CardTitle>
                  <div className="flex space-x-1 ml-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openDialog(item)}
                    >
                      <Edit2 className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(item.id)}
                    >
                      <Trash2 className="w-4 h-4 text-red-600" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-2">
                <p className="text-sm text-gray-600 line-clamp-3">
                  {item.description}
                </p>
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {item.activity_type}
                  </span>
                  <div className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    {formatDate(item.created_at)}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card className="col-span-full">
            <CardContent className="text-center py-12">
              <ImageIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 text-lg">Belum ada item galeri</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
