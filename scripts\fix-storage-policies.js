const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");

// Load environment variables manually
const envPath = path.join(__dirname, "..", ".env");
const envContent = fs.readFileSync(envPath, "utf8");
const envVars = {};

envContent.split("\n").forEach((line) => {
  const [key, value] = line.split("=");
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});

const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = envVars.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing Supabase environment variables");
  console.log("Make sure you have NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env file");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixStoragePolicies() {
  console.log("🔧 Fixing storage RLS policies...");
  
  try {
    // Test current upload capability
    console.log("\n1. Testing current upload capability...");
    
    const testFile = Buffer.from("test file content");
    const testPath = `imageFolder/gallery/test-${Date.now()}.txt`;
    
    const { error: testError } = await supabase.storage
      .from('images')
      .upload(testPath, testFile, {
        contentType: 'text/plain'
      });

    if (testError) {
      console.error("❌ Upload test failed:", testError.message);
      
      if (testError.message.includes('row-level security policy')) {
        console.log("\n📝 RLS Policy issue detected. Manual fix required:");
        console.log("\nGo to your Supabase Dashboard:");
        console.log("1. Navigate to Storage > Policies");
        console.log("2. Click on 'objects' table");
        console.log("3. Add these policies:\n");
        
        console.log("Policy 1: Allow public read");
        console.log("CREATE POLICY \"Allow public read\" ON storage.objects");
        console.log("FOR SELECT USING (bucket_id = 'images');\n");
        
        console.log("Policy 2: Allow authenticated insert");
        console.log("CREATE POLICY \"Allow authenticated insert\" ON storage.objects");
        console.log("FOR INSERT WITH CHECK (bucket_id = 'images' AND auth.role() = 'authenticated');\n");
        
        console.log("Policy 3: Allow authenticated update");
        console.log("CREATE POLICY \"Allow authenticated update\" ON storage.objects");
        console.log("FOR UPDATE USING (bucket_id = 'images' AND auth.role() = 'authenticated');\n");
        
        console.log("Policy 4: Allow authenticated delete");
        console.log("CREATE POLICY \"Allow authenticated delete\" ON storage.objects");
        console.log("FOR DELETE USING (bucket_id = 'images' AND auth.role() = 'authenticated');\n");
        
        console.log("Alternative: Disable RLS temporarily");
        console.log("ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;");
        console.log("(Not recommended for production)\n");
      }
    } else {
      console.log("✅ Upload test successful - RLS policies are working");
      
      // Clean up test file
      await supabase.storage
        .from('images')
        .remove([testPath]);
    }

    // Test with actual authentication
    console.log("\n2. Testing with authentication...");
    
    // Try to create a user session for testing
    const { data: authData, error: authError } = await supabase.auth.signInAnonymously();
    
    if (authError) {
      console.log("⚠️  Anonymous auth not enabled, testing with service key");
    } else {
      console.log("✅ Authentication test successful");
    }

    console.log("\n🎯 Quick Fix Options:");
    console.log("Option 1: Run the SQL commands above in Supabase Dashboard");
    console.log("Option 2: Temporarily disable RLS for testing");
    console.log("Option 3: Check if you're properly authenticated when uploading");

  } catch (error) {
    console.error("❌ Unexpected error:", error);
  }
}

// Run the fix
if (require.main === module) {
  fixStoragePolicies();
}

module.exports = { fixStoragePolicies };
