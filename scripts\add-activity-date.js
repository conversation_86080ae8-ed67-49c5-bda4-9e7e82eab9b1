const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");

// Load environment variables manually
const envPath = path.join(__dirname, "..", ".env");
const envContent = fs.readFileSync(envPath, "utf8");
const envVars = {};

envContent.split("\n").forEach((line) => {
  const [key, value] = line.split("=");
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});

const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = envVars.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing Supabase environment variables");
  console.log("Make sure you have NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env file");
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addActivityDateColumn() {
  console.log("🔧 Adding activity_date column to gallery table...");
  
  try {
    // First, check if the column already exists
    console.log("\n1. Checking if activity_date column exists...");
    
    const { data: existingData, error: selectError } = await supabase
      .from('gallery')
      .select('activity_date')
      .limit(1);

    if (selectError && selectError.message.includes('column "activity_date" does not exist')) {
      console.log("📝 Column doesn't exist, need to add it manually");
      console.log("\nPlease run this SQL in your Supabase dashboard:");
      console.log("ALTER TABLE gallery ADD COLUMN activity_date date NOT NULL DEFAULT CURRENT_DATE;");
      console.log("\nAfter adding the column, run this script again to update existing records.");
      return;
    } else if (selectError) {
      console.error("❌ Error checking column:", selectError.message);
      return;
    } else {
      console.log("✅ activity_date column already exists");
    }

    // Update existing records that don't have activity_date set
    console.log("\n2. Updating existing records...");
    
    const { data: recordsToUpdate, error: fetchError } = await supabase
      .from('gallery')
      .select('id, created_at, activity_date')
      .is('activity_date', null);

    if (fetchError) {
      console.error("❌ Error fetching records:", fetchError.message);
      return;
    }

    if (recordsToUpdate && recordsToUpdate.length > 0) {
      console.log(`📝 Found ${recordsToUpdate.length} records to update`);
      
      for (const record of recordsToUpdate) {
        // Set activity_date to the same as created_at date
        const activityDate = new Date(record.created_at).toISOString().split('T')[0];
        
        const { error: updateError } = await supabase
          .from('gallery')
          .update({ activity_date: activityDate })
          .eq('id', record.id);

        if (updateError) {
          console.error(`❌ Error updating record ${record.id}:`, updateError.message);
        } else {
          console.log(`✅ Updated record ${record.id} with date ${activityDate}`);
        }
      }
    } else {
      console.log("✅ All records already have activity_date set");
    }

    // Test the updated structure
    console.log("\n3. Testing updated structure...");
    
    const { data: testData, error: testError } = await supabase
      .from('gallery')
      .select('id, title, activity_date, created_at')
      .limit(3);

    if (testError) {
      console.error("❌ Error testing structure:", testError.message);
    } else {
      console.log("✅ Structure test successful");
      console.log("Sample records:");
      testData.forEach(record => {
        console.log(`  - ${record.title}: ${record.activity_date} (created: ${record.created_at.split('T')[0]})`);
      });
    }

    console.log("\n🎉 Activity date column setup completed!");
    console.log("You can now add past events with custom dates in the admin panel.");

  } catch (error) {
    console.error("❌ Unexpected error:", error);
    console.log("\n📝 Manual Setup Required:");
    console.log("1. Go to Supabase Dashboard > SQL Editor");
    console.log("2. Run: ALTER TABLE gallery ADD COLUMN activity_date date NOT NULL DEFAULT CURRENT_DATE;");
    console.log("3. Run this script again to update existing records");
  }
}

// Run the update
if (require.main === module) {
  addActivityDateColumn();
}

module.exports = { addActivityDateColumn };
