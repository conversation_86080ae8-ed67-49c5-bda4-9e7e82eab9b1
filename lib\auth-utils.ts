import { supabase } from "./supabase";

export interface PasswordResetResult {
  success: boolean;
  error?: string;
}

export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Send password reset email to the specified email address
 * @param email - The email address to send the reset link to
 * @param redirectTo - The URL to redirect to after clicking the reset link
 * @returns Promise with success status and optional error message
 */
export async function sendPasswordResetEmail(
  email: string,
  redirectTo?: string
): Promise<PasswordResetResult> {
  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo:
        redirectTo || `${window.location.origin}/admin/reset-password/confirm`,
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      error: "Terjadi kesalahan saat mengirim email reset password",
    };
  }
}

/**
 * Update user password
 * @param newPassword - The new password to set
 * @returns Promise with success status and optional error message
 */
export async function updateUserPassword(
  newPassword: string
): Promise<PasswordResetResult> {
  try {
    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      error: "Terjadi kesalahan saat mengupdate password",
    };
  }
}

/**
 * Validate password strength
 * @param password - The password to validate
 * @returns Validation result with isValid flag and array of error messages
 */
export function validatePasswordStrength(
  password: string
): PasswordValidationResult {
  const errors: string[] = [];

  // Minimum length check
  if (password.length < 8) {
    errors.push("Password minimal 8 karakter");
  }

  // Maximum length check (for security)
  if (password.length > 128) {
    errors.push("Password maksimal 128 karakter");
  }

  // Uppercase letter check
  if (!/[A-Z]/.test(password)) {
    errors.push("Password harus mengandung minimal 1 huruf besar");
  }

  // Lowercase letter check
  if (!/[a-z]/.test(password)) {
    errors.push("Password harus mengandung minimal 1 huruf kecil");
  }

  // Number check
  if (!/\d/.test(password)) {
    errors.push("Password harus mengandung minimal 1 angka");
  }

  // Special character check removed as per user request

  // Common password patterns check
  const commonPatterns = [
    /^password/i,
    /^admin/i,
    /^123456/,
    /^qwerty/i,
    /^abc123/i,
  ];

  for (const pattern of commonPatterns) {
    if (pattern.test(password)) {
      errors.push("Password terlalu umum, gunakan kombinasi yang lebih unik");
      break;
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Check if current session is valid for password reset
 * @returns Promise with session validation result
 */
export async function validateResetSession(): Promise<{
  isValid: boolean;
  error?: string;
}> {
  try {
    const {
      data: { session },
      error,
    } = await supabase.auth.getSession();

    if (error) {
      return {
        isValid: false,
        error: error.message,
      };
    }

    if (!session) {
      return {
        isValid: false,
        error: "Session tidak valid atau sudah kedaluwarsa",
      };
    }

    return {
      isValid: true,
    };
  } catch (error) {
    return {
      isValid: false,
      error: "Terjadi kesalahan saat memvalidasi session",
    };
  }
}

/**
 * Sign out user and clear session
 * @returns Promise with sign out result
 */
export async function signOutUser(): Promise<PasswordResetResult> {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      error: "Terjadi kesalahan saat logout",
    };
  }
}

/**
 * Check if email is valid format
 * @param email - Email to validate
 * @returns Boolean indicating if email format is valid
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Get password strength score (0-4)
 * @param password - Password to evaluate
 * @returns Strength score and description
 */
export function getPasswordStrength(password: string): {
  score: number;
  description: string;
  color: string;
} {
  let score = 0;

  // Length check
  if (password.length >= 8) score++;
  if (password.length >= 12) score++;

  // Character variety checks
  if (/[A-Z]/.test(password)) score++;
  if (/[a-z]/.test(password)) score++;
  if (/\d/.test(password)) score++;

  // Reduce score for common patterns
  const commonPatterns = [/^password/i, /^admin/i, /^123456/, /^qwerty/i];

  for (const pattern of commonPatterns) {
    if (pattern.test(password)) {
      score = Math.max(0, score - 2);
      break;
    }
  }

  // Normalize score to 0-4 range
  score = Math.min(4, Math.max(0, score - 2));

  const strengthLevels = [
    { description: "Sangat Lemah", color: "red" },
    { description: "Lemah", color: "orange" },
    { description: "Sedang", color: "yellow" },
    { description: "Kuat", color: "blue" },
    { description: "Sangat Kuat", color: "green" },
  ];

  return {
    score,
    description: strengthLevels[score].description,
    color: strengthLevels[score].color,
  };
}
